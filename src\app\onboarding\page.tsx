"use client";
import React from 'react';
import { OnboardingProvider } from '@/features/onboarding/OnboardingContext';
import { OnboardingStepper } from '@/features/onboarding/OnboardingStepper';
import dynamic from 'next/dynamic';

const steps = [
  dynamic(() => import('@/features/onboarding/steps/Step1')),
  dynamic(() => import('@/features/onboarding/steps/Step2')),
  dynamic(() => import('@/features/onboarding/steps/Step3')),
  dynamic(() => import('@/features/onboarding/steps/Step4')),
  dynamic(() => import('@/features/onboarding/steps/Step5')),
  dynamic(() => import('@/features/onboarding/steps/Step6')),
  dynamic(() => import('@/features/onboarding/steps/Step7')),
];

import { useOnboarding } from '@/features/onboarding/OnboardingContext';

function OnboardingSteps() {
  const { step } = useOnboarding();
  const StepComponent = steps[step - 1];

  return (
    <div className="w-full min-h-screen flex flex-col items-center justify-center bg-background text-foreground">
      <div className="w-full max-w-md px-4 py-10">
        <div className="mb-6">
          <div className="text-sm text-muted-foreground mb-2">Step {step}/7</div>
          <OnboardingStepper />
        </div>
        <StepComponent />
      </div>
    </div>
  );
}

export default function OnboardingPage() {
  return (
    <OnboardingProvider>
      <OnboardingSteps />
    </OnboardingProvider>
  );
}
