import { ArtistDetailsPage } from "@/features/artist/components";
import { client } from "@/graphql-client";
import { getAllArtist } from "@/graphql/queries";

// Generate static params for static export
export async function generateStaticParams() {
  try {
    // Fetch artists from API for static generation
    const result = await client.graphql({
      query: getAllArtist,
      variables: {},
    });

    // Process data regardless of errors
    return processStaticParams(result);

  } catch (error: unknown) {
    // Even if GraphQL throws an error, check if we have data
    const errorWithData = error as { data?: { getAllArtist?: unknown[] } };
    if (errorWithData?.data?.getAllArtist) {
      return processStaticParams(errorWithData);
    } else {
      console.error("True network error - no data available for static generation:", error);
      return [];
    }
  }
}

function processStaticParams(result: { data?: { getAllArtist?: unknown[] } }) {
  const artists = result?.data?.getAllArtist || [];
  const validArtists = artists
    .filter((artist) => artist && typeof (artist as { id?: unknown }).id === 'string')
    .map((artist) => ({ id: (artist as { id: string }).id }));

  console.log(`✅ Generated static params for ${validArtists.length} artists`);
  return validArtists;
}

export default function ArtistDetails() {
  return <ArtistDetailsPage />;
}
