'use client';
import React, { useState } from "react";
import DiscoverContextPanel from "./DiscoverContextPanel";
import DiscoverMainContent from "./DiscoverMainContent";

export default function DiscoverPage() {
  const [activeSection, setActiveSection] = useState("Feed");
  return (
    <div className="min-h-screen bg-background text-foreground flex">
      {/* Left contextual panel */}
      <DiscoverContextPanel active={activeSection} onNav={setActiveSection} />
      {/* Main dashboard content */}
      <DiscoverMainContent />
    </div>
  );
} 