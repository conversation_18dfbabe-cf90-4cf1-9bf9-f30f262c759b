import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useOnboarding } from '@/features/onboarding/OnboardingContext';

const roles = [
  'Singer', 'Song Writer', 'Producer',
  'Guitarist', 'Engineer', 'Drummer',
  'Bassist', 'Collaborator', 'Other',
];

export default function Step4() {
  const { step, setStep, data, setData } = useOnboarding();
  const selected = data.roles || [];

  const toggle = (role: string) => {
    setData({ roles: selected.includes(role) ? selected.filter(r => r !== role) : [...selected, role] });
  };

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Pick roles that fit you</h1>
      <div className="text-sm text-muted-foreground mb-4">Select one or more roles that match your vibe</div>
      <div className="flex w-full flex-wrap gap-2 mb-10">
        {roles.map(role => (
          <Badge
            key={role}
            variant={selected.includes(role) ? 'default' : 'outline'}
            className={`cursor-pointer text-center py-2 px-4 text-sm font-medium rounded transition select-none
              ${selected.includes(role)
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-card text-foreground border-border hover:border-primary'}`}
            onClick={() => toggle(role)}
          >
            {role}
          </Badge>
        ))}
      </div>
      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>Back</Button>
        <Button className="w-32" onClick={() => setStep(step + 1)} disabled={selected.length === 0}>Continue</Button>
      </div>
    </div>
  );
}
