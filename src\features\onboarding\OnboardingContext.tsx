import React, { createContext, useContext, useState, ReactNode } from 'react';

interface OnboardingData {
  realName: string;
  artistName: string;
  profilePhoto?: File | null;
  bio?: string;
  roles?: string[];
  genres?: string[];
  artistProfileType?: 'search' | 'url';
  artistProfileSearch?: string;
  artistProfileUrl?: string;
  noArtistProfile?: boolean;
  followArtists?: string[];
  // Add more fields for other steps as needed
}

interface OnboardingContextType {
  step: number;
  setStep: (step: number) => void;
  totalSteps: number;
  data: OnboardingData;
  setData: (data: Partial<OnboardingData>) => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

export const OnboardingProvider = ({ children }: { children: ReactNode }) => {
  const [step, setStep] = useState(1);
  const totalSteps = 7;
  const [data, setDataState] = useState<OnboardingData>({
    realName: '',
    artistName: '',
    profilePhoto: null,
    bio: '',
  });

  const setData = (newData: Partial<OnboardingData>) => {
    setDataState(prev => ({ ...prev, ...newData }));
  };

  return (
    <OnboardingContext.Provider value={{ step, setStep, totalSteps, data, setData }}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (!context) throw new Error('useOnboarding must be used within OnboardingProvider');
  return context;
};
