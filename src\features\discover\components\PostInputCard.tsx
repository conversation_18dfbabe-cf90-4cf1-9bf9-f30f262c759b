import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar } from "@/components/ui/avatar";
import { Image as ImageIcon, Link as LinkIcon } from "lucide-react";

interface PostInputCardProps {
  tabs: string[];
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  placeholder?: string;
}

const PostInputCard: React.FC<PostInputCardProps> = ({
  tabs,
  activeTab = tabs[0],
  onTabChange,
  placeholder = "What's new?",
}) => (
  <Card className="p-6 flex flex-col gap-4 rounded-l shadow-sm">
    <div className="flex items-center gap-4">
      <div className="flex gap-2">
        {tabs.map((tab) => (
          <Button
            key={tab}
            variant={tab === activeTab ? "secondary" : "ghost"}
            size="sm"
            className={`rounded-full font-semibold px-6 ${tab === activeTab ? "bg-foreground text-background" : ""}`}
            onClick={() => onTabChange?.(tab)}
          >
            {tab}
          </Button>
        ))}
      </div>
    </div>
    <div className="flex items-center gap-3 mt-2 ">
      <Avatar className="w-10 h-10 rounded-full bg-muted" />
      <div className="bg-muted rounded flex items-center gap-3 w-full">
        <Input placeholder={placeholder} className="flex-1 px-4 border-none" />
        <Button variant="ghost" size="icon" className="ml-2"><ImageIcon className="w-5 h-5" /></Button>
        <Button variant="ghost" size="icon"><LinkIcon className="w-5 h-5" /></Button>
      </div>
    </div>
  </Card>
);

export default PostInputCard; 