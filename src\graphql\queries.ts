/* tslint:disable */
/* eslint-disable */
// this is an auto generated file. This will be overwritten

import * as APITypes from "../API";
type GeneratedQuery<InputType, OutputType> = string & {
  __generatedQueryInput: InputType;
  __generatedQueryOutput: OutputType;
};

export const getAllArtist = /* GraphQL */ `query GetAllArtist {
  getAllArtist {
    id
    name
    bio
    formedDate
    disbandedDate
    location
    __typename
  }
}
` as GeneratedQuery<
  APITypes.GetAllArtistQueryVariables,
  APITypes.GetAllArtistQuery
>;
export const searchArtistByName = /* GraphQL */ `query SearchArtistByName($name: String!) {
  searchArtistByName(name: $name) {
    id
    name
    bio
    formedDate
    disbandedDate
    location
    __typename
  }
}
` as GeneratedQuery<
  APITypes.SearchArtistByNameQueryVariables,
  APITypes.SearchArtistByNameQuery
>;
export const getArtistProfile = /* GraphQL */ `query GetArtistProfile($artistId: ID!) {
  getArtistProfile(artistId: $artistId) {
    artist {
      id
      name
      bio
      formedDate
      disbandedDate
      location
      __typename
    }
    songs {
      id
      title
      duration
      recordID
      releaseDate
      coverPhoto
      role
      credits{
        artistId
        role
        name
      }
      __typename
    }
      

    albums {
      id
      title
      releaseDate
      genre
      description
      coverArtData
      __typename
    }
    __typename
  }
}
` as GeneratedQuery<
  APITypes.GetArtistProfileQueryVariables,
  APITypes.GetArtistProfileQuery
>;
export const getAlbum = /* GraphQL */ `query GetAlbum($albumId: ID!) {
  getAlbum(albumId: $albumId) {
    id
    title
    releaseDate
    genre
    description
    coverArtData
    tracks {
      trackPosition
        recording{
        id
        title	
        duration
        releaseDate
      }
      __typename
    }
    __typename
  }
}
` as GeneratedQuery<APITypes.GetAlbumQueryVariables, APITypes.GetAlbumQuery>;
