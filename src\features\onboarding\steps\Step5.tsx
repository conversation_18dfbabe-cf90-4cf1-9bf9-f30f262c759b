import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useOnboarding } from '@/features/onboarding/OnboardingContext';

const genres = [
  'Rock', 'Pop', 'R&B', 'Hip-Hop',
  'Indie', 'Electronic', 'Classical',
  'Jazz', 'Other',
];

export default function Step5() {
  const { step, setStep, data, setData } = useOnboarding();
  const selected = data.genres || [];

  const toggle = (genre: string) => {
    setData({ genres: selected.includes(genre) ? selected.filter(g => g !== genre) : [...selected, genre] });
  };

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Pick genres that vibe</h1>
      <div className="text-sm text-muted-foreground mb-4">Select one or more genres that match your vibe</div>
      <div className="flex w-full flex-wrap gap-2 mb-10">
        {genres.map(genre => (
          <Badge
            key={genre}
            variant={selected.includes(genre) ? 'default' : 'outline'}
            className={`cursor-pointer text-center py-2 px-4 text-sm font-medium rounded transition select-none
              ${selected.includes(genre)
                ? 'bg-primary text-primary-foreground border-primary'
                : 'bg-card text-foreground border-border hover:border-primary'}`}
            onClick={() => toggle(genre)}
          >
            {genre}
          </Badge>
        ))}
      </div>
      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>Back</Button>
        <Button className="w-32" onClick={() => setStep(step + 1)} disabled={selected.length === 0}>Continue</Button>
      </div>
    </div>
  );
}
