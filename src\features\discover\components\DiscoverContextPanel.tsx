import { UserCircle, Music, Users, Calendar } from "lucide-react";
import React from "react";

const navItems = [
  { label: "Feed", icon: UserCircle },
  { label: "Tracks", icon: Music },
  { label: "Artists", icon: Users },
  { label: "Gigs", icon: Calendar },
];

export default function DiscoverContextPanel({ active = "Feed", onNav }: { active?: string; onNav?: (label: string) => void }) {
  return (
    <aside className="w-72 min-w-[16rem] max-w-xs border-r bg-background/50 p-6 flex flex-col gap-6">
      <h2 className="text-xl font-bold mb-2">Discover</h2>
      <nav className="flex flex-col gap-1">
        {navItems.map((item) => {
          const isActive = active === item.label;
          return (
            <button
              key={item.label}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition text-left font-medium text-base hover:bg-muted ${isActive ? "bg-muted text-primary" : "text-muted-foreground"}`}
              onClick={() => onNav?.(item.label)}
              type="button"
            >
              <item.icon className="w-5 h-5" />
              {item.label}
            </button>
          );
        })}
      </nav>
    </aside>
  );
} 