"use client"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import {
  Play,
  Pause,
  Heart,
  Plus,
  Edit3,
  Share2,
  Clock,
  Calendar,
  Music,
  Disc,
  Users,
  Award,
  ListMusic,
  ExternalLink,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { MusicPlayer } from "../../../components/shared/music-player/music-player"
import { useMusicPlayer, type Song } from "@/contexts/music-player-context/music-player-context"
import { Recording } from "@/API"

export default function SongPage() {
      const router = useRouter();
      const { playSong, currentSong, isPlaying, togglePlay } = useMusicPlayer();
      
      const songData: Song = {
        id: "1",
        title: "Electric Skin",
        artist: "<PERSON>",
        album: "Neon Nightfall",
        albumArt: "/dummy-image.png",
        duration: 232, // 3:52 in seconds
        audioSrc: "/dummy-song.mp3",
        credits: {
          producer: "<PERSON>",
          writer: "Elena Rivers",
          engineer: "SoundForge LA"
        }
      };

      const handlePlayClick = () => {
        if (currentSong?.id === songData.id) {
          togglePlay();
        } else {
          playSong(songData);
        }
      };

      const handleArtistClick = () => {
        router.push(`/artist`);
      };
      const handleAlbumClick = () => {
        router.push(`/album`);
      };
      const handlePlaylistClick = () => {
        router.push(`/playlist`);
      };

      const recordings: Recording[] = []
  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header / Hero Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-8">
          {/* Album Cover */}
          <div className="flex-shrink-0">
            <Image
              src="/dummy-image.png?height=300&width=300"
              alt="Song Cover"
              width={300}
              height={300}
              className="rounded-lg shadow-lg"
            />
          </div>

          {/* Song Info */}
          <div className="flex-1 space-y-6">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold">Electric Skin</h1>
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-xl text-muted-foreground">by</span>
                <span className="text-xl text-primary cursor-pointer hover:underline font-medium" onClick={handleArtistClick}>Elena Rivers</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">
              <Button size="lg" className="gap-2" onClick={handlePlayClick}>
                {currentSong?.id === songData.id && isPlaying ? (
                  <>
                    <Pause className="w-5 h-5" />
                    Pause
                  </>
                ) : (
                  <>
                    <Play className="w-5 h-5" />
                    Play
                  </>
                )}
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Heart className="w-5 h-5" />
                Like
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Plus className="w-5 h-5" />
                Add to Playlist
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Edit3 className="w-5 h-5" />
                Edit
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <Share2 className="w-5 h-5" />
                Share
              </Button>
            </div>

            {/* Song Metadata */}
            <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>3:52</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>November 10, 2020</span>
              </div>
              <div className="flex items-center gap-2">
                <Music className="w-4 h-4" />
                <span>ISRC: US-X1Y-20-00001</span>
              </div>
            </div>

            {/* Genres */}
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary">Synthwave</Badge>
              <Badge variant="secondary">Pop</Badge>
              <Badge variant="secondary">Electronic</Badge>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Album Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Disc className="w-5 h-5" />
                  Album
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4">
                  <Image
                    src="/dummy-image.png?height=80&width=80"
                    alt="Album Cover"
                    width={80}
                    height={80}
                    className="rounded-lg"
                  />
                  <div className="space-y-1">
                    <h3 className="font-semibold text-primary cursor-pointer hover:underline">Neon Nightfall</h3>
                    <p className="text-sm text-muted-foreground">Track 1 of 3</p>
                    <p className="text-sm text-muted-foreground">Released November 2020</p>
                  </div>
                  <Button variant="ghost" size="sm" className="ml-auto" onClick={()=> handleAlbumClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Performed By */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Performed By
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                    <AvatarImage src="/placeholder.svg?height=40&width=40" />
                      <AvatarFallback>ER</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Elena Rivers</p>
                      <p className="text-sm text-muted-foreground">Lead Vocals, Writer</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm"
                    onClick={()=>handleArtistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                {/* <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" />
                      <AvatarFallback>SM</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Sarah Mitchell</p>
                      <p className="text-sm text-muted-foreground">Backing Vocals, Synthesizer</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm"
                                    onClick={()=>handleArtistClick()}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src="/placeholder.svg?height=40&width=40" />
                      <AvatarFallback>MR</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Mike Rodriguez</p>
                      <p className="text-sm text-muted-foreground">Bass Guitar</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm"
                                    onClick={()=>handleArtistClick()}
                                    >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div> */}
              </CardContent>
            </Card>
            {/* Recordings */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold flex items-center gap-2">
              <Music className="w-5 h-5" />
              Recordings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 h-[25rem] overflow-x-auto">
            {recordings.length > 0 ? (
              recordings.map((song, index) => (
                song && (
                  <div key={song.id} className="group">
                    <div className="rounded-lg border border-border/50 p-4 hover:border-border transition-colors hover:bg-muted/30">
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 mt-1">
                          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <Play className="w-4 h-4 text-primary" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-3">
                            <Music className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                            <h3 className="font-semibold text-lg truncate">
                              {song.title || "Unknown Song"}
                            </h3>
                          </div>

                          {song.credits && song.credits.length > 0 && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="w-4 h-4 rounded-full bg-secondary flex items-center justify-center">
                                  <span className="text-xs">👥</span>
                                </div>
                                <span className="text-sm font-medium text-muted-foreground">Credits</span>
                              </div>
                              <div className="grid gap-2 ml-6">
                                {/* {song.credits.map((credit, creditIndex) => (
                                  <div key={creditIndex} className="flex items-center gap-2">
                                    <div className="w-1.5 h-1.5 rounded-full bg-primary/60"></div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-auto p-0 text-sm font-normal justify-start hover:bg-transparent"
                                      onClick={() => router.push(`/artist/${credit.artistId}`)}
                                    >
                                      <span className="text-primary hover:underline">
                                        {artistNameMap[credit.artistId] || credit.artistId}
                                      </span>
                                      <Badge variant="secondary" className="ml-2 text-xs">
                                        {credit.role}
                                      </Badge>
                                    </Button>
                                  </div>
                                ))} */}
                              </div>
                            </div>
                          )}

                          {(!song.credits || song.credits.length === 0) && (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <div className="w-4 h-4 rounded-full bg-muted flex items-center justify-center">
                                <span className="text-xs">👥</span>
                              </div>
                              <span className="text-sm">No credits available</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {index < recordings.length - 1 && (
                      <Separator className="my-6" />
                    )}
                  </div>
                )
              ))
            ) : (
              <div className="text-center py-8">
                <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No recordings available</p>
              </div>
            )}
          </CardContent>
        </Card>

            {/* Credits */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5" />
                  Credits
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">Elena Rivers</p>
                      <p className="text-sm text-muted-foreground">Vocals, Songwriter</p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={()=>handleArtistClick()}>
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">Marcus Light</p>
                      <p className="text-sm text-muted-foreground">Producer</p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={()=>handleArtistClick()}>
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="font-medium text-primary cursor-pointer hover:underline">SoundForge LA</p>
                      <p className="text-sm text-muted-foreground">Mixing</p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={()=>handleArtistClick()}>
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                  
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Playlists Featuring This Song */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ListMusic className="w-5 h-5" />
                  Featured In Playlists
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Synthwave Sundays</p>
                      <p className="text-sm text-muted-foreground">247 songs</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={()=>handlePlaylistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">Alternative Vibes</p>
                      <p className="text-sm text-muted-foreground">156 songs</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={()=>handlePlaylistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                      <Music className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-primary cursor-pointer hover:underline">New Discoveries</p>
                      <p className="text-sm text-muted-foreground">89 songs</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={()=>handlePlaylistClick()}>
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>

                <Button variant="outline" className="w-full mt-4">
                  <Plus className="w-4 h-4 mr-2" />
                  Add to Playlist
                </Button>
              </CardContent>
            </Card>

            {/* Song Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Song Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Likes</span>
                  <span className="font-medium">1,247</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Playlist Adds</span>
                  <span className="font-medium">892</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Shares</span>
                  <span className="font-medium">156</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Similar Songs */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Similar Songs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 overflow-x-auto pb-4">
              {[1, 2, 3, 4, 5].map((i) => {
                const similarSong: Song = {
                  id: `similar-${i}`,
                  title: `Neon Nights ${i}`,
                  artist: "Various Artists",
                  album: "Electronic Collection",
                  albumArt: "/dummy-image.png",
                  duration: 225, // 3:45 in seconds
                  audioSrc: "/sample-audio.mp3"
                };

                return (
                  <div key={i} className="flex-shrink-0 w-48 space-y-3 cursor-pointer group">
                    <div className="relative">
                      <Image
                        src="/dummy-image.png?height=192&width=192"
                        alt="Song Cover"
                        width={192}
                        height={192}
                        className="rounded-lg group-hover:shadow-lg transition-shadow"
                      />
                      <Button
                        size="sm"
                        className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => playSong(similarSong)}
                      >
                        {currentSong?.id === similarSong.id && isPlaying ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    <div className="space-y-1">
                      <p className="font-medium text-sm group-hover:text-primary transition-colors">{similarSong.title}</p>
                      <p className="text-xs text-muted-foreground">{similarSong.artist}</p>
                      <p className="text-xs text-muted-foreground">3:45</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Editable Fields Section (for managers/editors) */}
        <Card className="mt-8 border-dashed border-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-muted-foreground">
              <Edit3 className="w-5 h-5" />
              Manager Tools
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <Button variant="outline" className="justify-start">
                <Edit3 className="w-4 h-4 mr-2" />
                Edit Song Metadata
              </Button>
              <Button variant="outline" className="justify-start">
                <Users className="w-4 h-4 mr-2" />
                Manage Performers
              </Button>
              <Button variant="outline" className="justify-start">
                <Award className="w-4 h-4 mr-2" />
                Edit Credits
              </Button>
              <Button variant="outline" className="justify-start">
                <Disc className="w-4 h-4 mr-2" />
                Update Album Info
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      <MusicPlayer/>
    </div>
  )
}
